# Quick script to complete the setup since Inputs.zip was already downloaded
Write-Host "Completing Elevate3D setup..." -ForegroundColor Green
Write-Host ""

# Check if Inputs.zip exists
if (Test-Path "Inputs.zip") {
    Write-Host "✓ Found Inputs.zip, extracting..." -ForegroundColor Blue
    try {
        Expand-Archive -Path "Inputs.zip" -DestinationPath "." -Force
        Remove-Item "Inputs.zip" -Force
        Write-Host "✓ Example data extracted successfully" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to extract: $_" -ForegroundColor Red
        Write-Host "Please extract Inputs.zip manually to the current directory" -ForegroundColor Yellow
        Read-Host "Press Enter to continue anyway"
    }
}

# Check if files were extracted to wrong location and fix it
Write-Host "Checking extraction location..." -ForegroundColor Blue
if ((Test-Path "2D") -and (Test-Path "3D") -and !(Test-Path "Inputs\2D")) {
    Write-Host "⚠ Files extracted to wrong location, moving to Inputs folder..." -ForegroundColor Yellow
    try {
        # Ensure Inputs directory exists
        if (!(Test-Path "Inputs")) {
            New-Item -ItemType Directory -Path "Inputs" -Force | Out-Null
        }

        # Move 2D and 3D directories to Inputs
        if (Test-Path "2D") {
            Move-Item "2D" "Inputs\" -Force
            Write-Host "  ✓ Moved 2D folder to Inputs\" -ForegroundColor Green
        }
        if (Test-Path "3D") {
            Move-Item "3D" "Inputs\" -Force
            Write-Host "  ✓ Moved 3D folder to Inputs\" -ForegroundColor Green
        }

        Write-Host "✓ Directory structure corrected" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to move directories: $_" -ForegroundColor Red
        Write-Host "Please manually move 2D and 3D folders into the Inputs directory" -ForegroundColor Yellow
    }
} elseif ((Test-Path "Inputs\2D") -and (Test-Path "Inputs\3D")) {
    Write-Host "✓ Example data already in correct location" -ForegroundColor Green
} else {
    Write-Host "⚠ Example data missing. Please download manually from:" -ForegroundColor Yellow
    Write-Host "https://drive.google.com/file/d/1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya/view?usp=sharing" -ForegroundColor Cyan
}

# Update config files for Windows
Write-Host ""
Write-Host "Updating configuration files for Windows..." -ForegroundColor Blue

$configFiles = @(
    "Configs\config_gso.yaml",
    "Configs\config_trellis.yaml"
)

$linuxPath = "./PoissonRecon/Bin/Linux/PoissonRecon"
$windowsPath = "./PoissonRecon/Bin/Windows/PoissonRecon.exe"

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        try {
            $content = Get-Content $configFile -Raw
            $updatedContent = $content -replace [regex]::Escape($linuxPath), $windowsPath
            Set-Content -Path $configFile -Value $updatedContent -NoNewline
            Write-Host "  ✓ Updated $configFile" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ Failed to update $configFile" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠ $configFile not found" -ForegroundColor Yellow
    }
}

# Verify setup
Write-Host ""
Write-Host "Verifying setup..." -ForegroundColor Blue

$checks = @(
    @{ Path = "Checkpoints\sam\sam_vit_h_4b8939.pth"; Name = "SAM checkpoint" },
    @{ Path = "PoissonRecon\Bin\Windows\PoissonRecon.exe"; Name = "PoissonRecon executable" },
    @{ Path = "Inputs\2D"; Name = "2D example data" },
    @{ Path = "Inputs\3D"; Name = "3D example data" }
)

$allGood = $true
foreach ($check in $checks) {
    if (Test-Path $check.Path) {
        Write-Host "  ✓ $($check.Name)" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $($check.Name) - MISSING" -ForegroundColor Red
        $allGood = $false
    }
}

Write-Host ""
if ($allGood) {
    Write-Host "🎉 Setup completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Next steps:" -ForegroundColor Yellow
    Write-Host "  1. Create conda environment: conda env create -f environment.yml --name elevate3d" -ForegroundColor White
    Write-Host "  2. Activate environment: conda activate elevate3d" -ForegroundColor White
    Write-Host "  3. Test PoissonRecon: .\PoissonRecon\Bin\Windows\PoissonRecon.exe --help" -ForegroundColor White
    Write-Host "  4. Run examples: python -m FLUX.flux_HFS-SDEdit" -ForegroundColor White
} else {
    Write-Host "⚠ Setup incomplete. Please address the missing items above." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to continue"
