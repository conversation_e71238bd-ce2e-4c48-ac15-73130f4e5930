# PowerShell setup script for Elevate3D on Windows
Write-Host "Setting up Elevate3D project for Windows..." -ForegroundColor Green
Write-Host ""

# Check if aria2c is available
try {
    $null = Get-Command aria2c -ErrorAction Stop
    Write-Host "✓ aria2c found" -ForegroundColor Green
} catch {
    Write-Host "✗ ERROR: aria2c is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install aria2c first:" -ForegroundColor Yellow
    Write-Host "  Option 1: winget install aria2.aria2" -ForegroundColor Yellow
    Write-Host "  Option 2: Download from https://aria2.github.io/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create necessary directories
Write-Host "Creating directory structure..." -ForegroundColor Blue
$directories = @(
    "Checkpoints\sam",
    "PoissonRecon\Bin\Windows",
    "Inputs"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  Created: $dir" -ForegroundColo<PERSON> Gray
    } else {
        Write-Host "  Exists: $dir" -ForegroundColor Gray
    }
}

# Download SAM checkpoint
Write-Host ""
Write-Host "Downloading SAM checkpoint..." -ForegroundColor Blue
$samPath = "Checkpoints\sam\sam_vit_h_4b8939.pth"
if (!(Test-Path $samPath)) {
    & aria2c -x 8 -s 8 -d "Checkpoints\sam" -o "sam_vit_h_4b8939.pth" "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: Failed to download SAM checkpoint" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "✓ SAM checkpoint downloaded" -ForegroundColor Green
} else {
    Write-Host "✓ SAM checkpoint already exists" -ForegroundColor Green
}

# Download PoissonRecon prebuilt executables
Write-Host ""
Write-Host "Downloading PoissonRecon prebuilt executables..." -ForegroundColor Blue
$poissonExe = "PoissonRecon\Bin\Windows\PoissonRecon.exe"
if (!(Test-Path $poissonExe)) {
    & aria2c -x 8 -s 8 -d "." -o "AdaptiveSolvers.x64.zip" "https://www.cs.jhu.edu/~misha/Code/PoissonRecon/Version18.74/AdaptiveSolvers.x64.zip"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ ERROR: Failed to download PoissonRecon executables" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }

    # Extract PoissonRecon executables
    Write-Host "Extracting PoissonRecon executables..." -ForegroundColor Blue
    try {
        Expand-Archive -Path "AdaptiveSolvers.x64.zip" -DestinationPath "temp_extract" -Force
        
        # Move executables to correct location
        Write-Host "Moving executables to PoissonRecon\Bin\Windows\..." -ForegroundColor Blue
        Get-ChildItem "temp_extract\AdaptiveSolvers.x64\*" | Move-Item -Destination "PoissonRecon\Bin\Windows\" -Force
        
        # Clean up
        Remove-Item "temp_extract" -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item "AdaptiveSolvers.x64.zip" -Force -ErrorAction SilentlyContinue
        
        Write-Host "✓ PoissonRecon executables installed" -ForegroundColor Green
    } catch {
        Write-Host "✗ ERROR: Failed to extract PoissonRecon executables: $_" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "✓ PoissonRecon executables already exist" -ForegroundColor Green
}

# Download example data from Google Drive
Write-Host ""
Write-Host "Downloading example data from Google Drive..." -ForegroundColor Blue

# Google Drive file ID
$googleDriveFileId = "1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya"

if (!(Test-Path "Inputs\2D") -or !(Test-Path "Inputs\3D")) {

    # Function to download from Google Drive with proper handling
    function Download-GoogleDriveFile {
        param($FileId, $OutputPath)

        try {
            # Method 1: Try gdown if available (most reliable for Google Drive)
            Write-Host "Checking for gdown (Google Drive downloader)..." -ForegroundColor Gray
            $gdownAvailable = $false
            try {
                $null = Get-Command gdown -ErrorAction Stop
                $gdownAvailable = $true
                Write-Host "✓ gdown found, using it for download..." -ForegroundColor Green
            } catch {
                Write-Host "gdown not found, trying alternative methods..." -ForegroundColor Gray
            }

            if ($gdownAvailable) {
                & gdown --id $FileId --output $OutputPath
                if ($LASTEXITCODE -eq 0 -and (Test-Path $OutputPath)) {
                    return $true
                }
            }

            # Method 2: Try with curl following redirects and handling confirmation
            Write-Host "Attempting download with curl (following redirects)..." -ForegroundColor Gray
            $initialUrl = "https://drive.google.com/uc?export=download&id=$FileId"

            # First request to get the confirmation token
            $tempHtml = "temp_gdrive.html"
            & curl -c "cookies.txt" -s -L -o $tempHtml $initialUrl

            if (Test-Path $tempHtml) {
                $htmlContent = Get-Content $tempHtml -Raw

                # Look for confirmation token in the HTML
                if ($htmlContent -match 'confirm=([^&"]+)') {
                    $confirmToken = $matches[1]
                    $confirmUrl = "https://drive.google.com/uc?export=download&confirm=$confirmToken&id=$FileId"

                    Write-Host "Found confirmation token, downloading file..." -ForegroundColor Gray
                    & curl -b "cookies.txt" -L -o $OutputPath $confirmUrl

                    # Clean up temp files
                    Remove-Item $tempHtml -Force -ErrorAction SilentlyContinue
                    Remove-Item "cookies.txt" -Force -ErrorAction SilentlyContinue

                    if ($LASTEXITCODE -eq 0 -and (Test-Path $OutputPath)) {
                        return $true
                    }
                } else {
                    # Try direct download (might work for smaller files)
                    Write-Host "No confirmation needed, trying direct download..." -ForegroundColor Gray
                    & curl -L -o $OutputPath $initialUrl

                    Remove-Item $tempHtml -Force -ErrorAction SilentlyContinue

                    if ($LASTEXITCODE -eq 0 -and (Test-Path $OutputPath)) {
                        return $true
                    }
                }
            }

            # Method 3: Try with aria2c with better headers
            Write-Host "Attempting download with aria2c (with headers)..." -ForegroundColor Gray
            & aria2c --header="User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" --max-connection-per-server=1 --split=1 -d "." -o $OutputPath $initialUrl

            if ($LASTEXITCODE -eq 0 -and (Test-Path $OutputPath)) {
                return $true
            }

            return $false

        } catch {
            Write-Host "Download method failed: $_" -ForegroundColor Red
            return $false
        }
    }

    # Attempt the download
    $downloadSuccess = Download-GoogleDriveFile -FileId $googleDriveFileId -OutputPath "Inputs.zip"

    if ($downloadSuccess) {

        # Validate and extract the downloaded file
        Write-Host "Validating downloaded file..." -ForegroundColor Blue

        # Check file size (should be substantial for the dataset)
        $fileInfo = Get-Item "Inputs.zip"
        if ($fileInfo.Length -lt 1MB) {
            Write-Host "⚠ Downloaded file is too small ($($fileInfo.Length) bytes). Likely an error page." -ForegroundColor Yellow
            Remove-Item "Inputs.zip" -Force
            throw "Downloaded file appears to be invalid"
        }

        # Check if it's actually a zip file (Google Drive sometimes returns HTML)
        try {
            $fileHeader = Get-Content "Inputs.zip" -Head 1 -ErrorAction SilentlyContinue
            if ($fileHeader -like "*<html*" -or $fileHeader -like "*<!DOCTYPE*") {
                Write-Host "⚠ Google Drive returned HTML instead of file." -ForegroundColor Yellow
                Remove-Item "Inputs.zip" -Force
                throw "Google Drive anti-bot protection triggered"
            }
        } catch {
            # If we can't read the header, assume it's a binary zip file (which is good)
            Write-Host "File appears to be binary (likely a valid zip file)" -ForegroundColor Gray
        }

        # Extract the zip file
        Write-Host "Extracting Inputs.zip..." -ForegroundColor Blue
        try {
            # Extract to temporary location first
            $tempExtractPath = "temp_inputs_extract"
            Expand-Archive -Path "Inputs.zip" -DestinationPath $tempExtractPath -Force

            # Check the structure and move appropriately
            if (Test-Path "$tempExtractPath\Inputs") {
                # If zip contains Inputs folder, move its contents
                Write-Host "Moving extracted files to correct location..." -ForegroundColor Gray
                if (Test-Path "Inputs") {
                    Remove-Item "Inputs" -Recurse -Force
                }
                Move-Item "$tempExtractPath\Inputs" "." -Force
            } elseif ((Test-Path "$tempExtractPath\2D") -and (Test-Path "$tempExtractPath\3D")) {
                # If zip contains 2D and 3D directly, move them to Inputs
                Write-Host "Organizing files into Inputs directory..." -ForegroundColor Gray
                if (!(Test-Path "Inputs")) {
                    New-Item -ItemType Directory -Path "Inputs" -Force | Out-Null
                }
                Move-Item "$tempExtractPath\2D" "Inputs\" -Force
                Move-Item "$tempExtractPath\3D" "Inputs\" -Force
            } else {
                # Unknown structure, extract everything to Inputs
                Write-Host "Unknown zip structure, extracting to Inputs..." -ForegroundColor Gray
                if (Test-Path "Inputs") {
                    Remove-Item "Inputs" -Recurse -Force
                }
                Move-Item $tempExtractPath "Inputs" -Force
            }

            # Clean up
            if (Test-Path $tempExtractPath) {
                Remove-Item $tempExtractPath -Recurse -Force -ErrorAction SilentlyContinue
            }
            Remove-Item "Inputs.zip" -Force
            Write-Host "✓ Example data extracted successfully" -ForegroundColor Green
        } catch {
            Write-Host "⚠ Failed to extract zip file: $_" -ForegroundColor Yellow
            Write-Host "The file may be corrupted. Please try manual download." -ForegroundColor Yellow
            throw "Extraction failed"
        }

    } else {
        # All download methods failed
        Write-Host "⚠ All automatic download methods failed." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "This is common with Google Drive due to anti-bot protection." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "📥 Manual download instructions:" -ForegroundColor Cyan
        Write-Host "  1. Open this link in your browser:" -ForegroundColor White
        Write-Host "     https://drive.google.com/file/d/1VJmnT2UQKsYuZijGeAuJ0fOeOqkP8nya/view?usp=sharing" -ForegroundColor Blue
        Write-Host "  2. Click 'Download' button" -ForegroundColor White
        Write-Host "  3. Save the file as 'Inputs.zip' in this directory:" -ForegroundColor White
        Write-Host "     $PWD" -ForegroundColor Gray
        Write-Host "  4. Run this script again or use: .\complete_setup.ps1" -ForegroundColor White
        Write-Host ""
        Write-Host "💡 Alternative: Install gdown for better Google Drive support:" -ForegroundColor Yellow
        Write-Host "     pip install gdown" -ForegroundColor Gray
        Write-Host ""

        $response = Read-Host "Have you downloaded Inputs.zip manually to this directory? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            if (Test-Path "Inputs.zip") {
                Write-Host "✓ Found Inputs.zip, extracting..." -ForegroundColor Green
                try {
                    Expand-Archive -Path "Inputs.zip" -DestinationPath "." -Force
                    Remove-Item "Inputs.zip" -Force
                    Write-Host "✓ Example data extracted successfully" -ForegroundColor Green
                } catch {
                    Write-Host "⚠ Failed to extract: $_" -ForegroundColor Red
                    Write-Host "Please extract Inputs.zip manually to this directory" -ForegroundColor Yellow
                }
            } else {
                Write-Host "⚠ Inputs.zip not found in current directory" -ForegroundColor Yellow
                Write-Host "Please make sure the file is saved as 'Inputs.zip' in: $PWD" -ForegroundColor Gray
            }
        } else {
            Write-Host ""
            Write-Host "Setup will continue without example data." -ForegroundColor Yellow
            Write-Host "You can download it later and run: .\complete_setup.ps1" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "✓ Example data already exists" -ForegroundColor Green
}

# Update config files for Windows
Write-Host ""
Write-Host "Updating configuration files for Windows..." -ForegroundColor Blue

$configFiles = @(
    "Configs\config_gso.yaml",
    "Configs\config_trellis.yaml"
)

$linuxPath = "./PoissonRecon/Bin/Linux/PoissonRecon"
$windowsPath = "./PoissonRecon/Bin/Windows/PoissonRecon.exe"

foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        try {
            $content = Get-Content $configFile -Raw
            $updatedContent = $content -replace [regex]::Escape($linuxPath), $windowsPath
            Set-Content -Path $configFile -Value $updatedContent -NoNewline
            Write-Host "  ✓ Updated $configFile" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ Failed to update $configFile" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠ $configFile not found" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Complete setup finished successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "✅ What was set up:" -ForegroundColor Yellow
Write-Host "  ✓ SAM checkpoint downloaded" -ForegroundColor Gray
Write-Host "  ✓ PoissonRecon Windows executables installed" -ForegroundColor Gray
Write-Host "  ✓ Example data downloaded and extracted" -ForegroundColor Gray
Write-Host "  ✓ Config files updated for Windows paths" -ForegroundColor Gray
Write-Host ""
Write-Host "🚀 Next steps to start using Elevate3D:" -ForegroundColor Yellow
Write-Host "  1. Create conda environment: conda env create -f environment.yml --name elevate3d" -ForegroundColor White
Write-Host "  2. Activate environment: conda activate elevate3d" -ForegroundColor White
Write-Host "  3. Test the setup: .\PoissonRecon\Bin\Windows\PoissonRecon.exe --help" -ForegroundColor White
Write-Host "  4. Run examples: python -m FLUX.flux_HFS-SDEdit" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to continue"
